# Admin Content API

Документация по API для управления контентом в административной панели.

## Аутентификация

Все маршруты требуют аутентификации через Sanctum и роль `admin`.

```
Authorization: Bearer {token}
```

## Маршруты

### Получение списка контента

```http
GET /api/admin/content
```

**Параметры запроса:**
- `status` (string, optional) - Фильтр по статусу (`draft`, `published`)
- `search` (string, optional) - Поиск по заголовку
- `category` (string, optional) - Фильтр по slug категории
- `type` (string, optional) - Фильтр по типу контента
- `per_page` (integer, optional) - Количество элементов на странице (по умолчанию: 15)

**Пример ответа:**
```json
{
  "data": [
    {
      "id": 1,
      "content_category_id": 1,
      "title": "Заголовок статьи",
      "slug": "zagolovok-stati",
      "status": "published",
      "category": {
        "id": 1,
        "name": "Новости",
        "slug": "news"
      }
    }
  ],
  "links": {...},
  "meta": {...}
}
```

### Создание контента

```http
POST /api/admin/content
```

**Тело запроса:**
```json
{
  "content_category_id": 1,
  "title": "Заголовок статьи",
  "slug": "zagolovok-stati",
  "description": "Описание статьи",
  "content": "Содержимое статьи",
  "status": "draft",
  "meta_title": "Мета заголовок",
  "meta_description": "Мета описание",
  "type": "text",
  "blocks": [
    {
      "type": "text",
      "data": {
        "content": "Текст блока"
      },
      "position": 1
    }
  ]
}
```

### Получение контента

```http
GET /api/admin/content/{id}
```

### Обновление контента

```http
PUT /api/admin/content/{id}
```

### Удаление контента

```http
DELETE /api/admin/content/{id}
```

### Статистика

```http
GET /api/admin/content/stats
```

**Пример ответа:**
```json
{
  "data": {
    "total": 150,
    "published": 120,
    "draft": 30,
    "by_type": {
      "text": 100,
      "image": 30,
      "video": 20
    }
  }
}
```

### Массовое изменение статуса

```http
POST /api/admin/content/bulk-update-status
```

**Тело запроса:**
```json
{
  "content_ids": [1, 2, 3],
  "status": "published"
}
```

### Дублирование контента

```http
POST /api/admin/content/{id}/duplicate
```

### Изменение порядка блоков

```http
PATCH /api/admin/content/{id}/reorder-blocks
```

**Тело запроса:**
```json
{
  "blocks": [
    {
      "id": 1,
      "position": 2
    },
    {
      "id": 2,
      "position": 1
    }
  ]
}
```

## Типы блоков

### Text Block
```json
{
  "type": "text",
  "data": {
    "content": "HTML или Markdown содержимое"
  }
}
```

### Image Block
```json
{
  "type": "image",
  "data": {
    "url": "https://example.com/image.jpg",
    "alt": "Альтернативный текст",
    "caption": "Подпись к изображению"
  }
}
```

### Gallery Block
```json
{
  "type": "gallery",
  "data": {
    "images": [
      {
        "url": "https://example.com/image1.jpg",
        "alt": "Альтернативный текст 1"
      },
      {
        "url": "https://example.com/image2.jpg",
        "alt": "Альтернативный текст 2"
      }
    ]
  }
}
```

### Video Block
```json
{
  "type": "video",
  "data": {
    "url": "https://www.youtube.com/watch?v=VIDEO_ID",
    "title": "Заголовок видео"
  }
}
```

### Quote Block
```json
{
  "type": "quote",
  "data": {
    "text": "Текст цитаты",
    "author": "Автор цитаты"
  }
}
```

### Embed Block
```json
{
  "type": "embed",
  "data": {
    "code": "<iframe src='...'></iframe>"
  }
}
```

## Коды ошибок

- `400` - Ошибка валидации
- `401` - Не авторизован
- `403` - Недостаточно прав
- `404` - Контент не найден
- `422` - Ошибка валидации данных
- `500` - Внутренняя ошибка сервера
