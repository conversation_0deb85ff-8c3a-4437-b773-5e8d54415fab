<?php

use App\Http\Controllers\Admin\ContentController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Admin API Routes
|--------------------------------------------------------------------------
|
| Здесь регистрируются маршруты для административной панели.
| Все маршруты должны быть защищены соответствующими middleware.
|
*/

Route::middleware(['auth:sanctum', 'admin'])->group(function () {
    
    // Контент
    Route::apiResource('content', ContentController::class);
    
    // Дополнительные маршруты для контента
    Route::prefix('content')->name('content.')->group(function () {
        Route::get('stats', [ContentController::class, 'stats'])->name('stats');
        Route::post('bulk-update-status', [ContentController::class, 'bulkUpdateStatus'])->name('bulk-update-status');
        Route::post('{content}/duplicate', [ContentController::class, 'duplicate'])->name('duplicate');
        Route::patch('{content}/reorder-blocks', [ContentController::class, 'reorderBlocks'])->name('reorder-blocks');
    });
    
});
