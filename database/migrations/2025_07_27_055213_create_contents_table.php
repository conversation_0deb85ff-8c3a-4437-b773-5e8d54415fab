<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contents', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\ContentCategory::class);
            $table->string('title');
            $table->string('subtitle')->nullable();
            $table->string('slug')->unique();
            $table->string('description')->nullable();
            $table->text('content');
            $table->string('image_alt')->nullable();
            $table->timestamp('published_at')->nullable();
            $table->enum('status', ['draft', 'published'])->default('draft');
            $table->string('meta_title');
            $table->string('meta_description');
            $table->string('meta_keywords')->nullable();

            $table->json('settings')->nullable(); // например, цветовая схема
            $table->enum('type', [
                'text',        // обычный текст/markdown
                'image',       // одиночное изображение
                'gallery',     // несколько изображений
                'video',       // встроенное видео
                'quote',
                'embed'        // iframe, tweet и т.д.
            ])->index();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contents');
    }
};
