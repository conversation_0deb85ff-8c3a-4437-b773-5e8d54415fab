<?php

namespace Database\Factories;

use App\Models\Content;
use App\Models\ContentBlock;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ContentBlock>
 */
class ContentBlockFactory extends Factory
{
    protected $model = ContentBlock::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $type = $this->faker->randomElement(['text', 'image', 'gallery', 'video', 'quote', 'embed']);
        
        return [
            'content_id' => Content::factory(),
            'type' => $type,
            'data' => $this->generateDataForType($type),
            'position' => $this->faker->numberBetween(1, 10),
        ];
    }

    /**
     * Generate appropriate data based on block type.
     */
    private function generateDataForType(string $type): array
    {
        return match ($type) {
            'text' => [
                'content' => $this->faker->paragraphs(3, true),
            ],
            'image' => [
                'url' => $this->faker->imageUrl(),
                'alt' => $this->faker->sentence(),
                'caption' => $this->faker->optional()->sentence(),
            ],
            'gallery' => [
                'images' => array_map(fn() => [
                    'url' => $this->faker->imageUrl(),
                    'alt' => $this->faker->sentence(),
                ], range(1, $this->faker->numberBetween(2, 5))),
            ],
            'video' => [
                'url' => 'https://www.youtube.com/watch?v=' . $this->faker->regexify('[A-Za-z0-9_-]{11}'),
                'title' => $this->faker->sentence(),
            ],
            'quote' => [
                'text' => $this->faker->paragraph(),
                'author' => $this->faker->name(),
            ],
            'embed' => [
                'code' => '<iframe src="' . $this->faker->url() . '"></iframe>',
            ],
            default => [],
        };
    }

    /**
     * Create a text block.
     */
    public function text(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'text',
            'data' => [
                'content' => $this->faker->paragraphs(3, true),
            ],
        ]);
    }

    /**
     * Create an image block.
     */
    public function image(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'image',
            'data' => [
                'url' => $this->faker->imageUrl(),
                'alt' => $this->faker->sentence(),
                'caption' => $this->faker->optional()->sentence(),
            ],
        ]);
    }
}
