<?php

namespace App\Models;

use App\Traits\HasSlug;
use App\Traits\ImagesConversion350Trait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ContentCategory extends Model
{
    use HasSlug;
    use ImagesConversion350Trait;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'image',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    public function contents(): HasMany
    {
        return $this->hasMany(Content::class);
    }

    /**
     * Переопределяем поле для генерации slug
     */
    protected function getSlugSourceField(): string
    {
        return 'name';
    }
}
