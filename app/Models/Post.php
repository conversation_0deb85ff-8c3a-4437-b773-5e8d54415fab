<?php

namespace App\Models;

use App\Models\Index\IndexGunsBrokerPost;
use App\Traits\ImagesConversion350Trait;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Overtrue\LaravelFavorite\Traits\Favoriteable;

class Post extends ModerationModel
{
    use Favoriteable;
    use ImagesConversion350Trait;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'price',
        'year',
        'user_id',
        'views',
        'favorites',
        'category_id',
        'ref_city_id',
        'address',
        'address_geo',
        'source',
        'is_rebate', // Торг
        'is_trade', // Обмен
        'can_ship', // Отправка
        'moderation_id',
        'geo_item_id',
        'fingerprint',
        'registration_type',
        'registration_address',
        'registration_geo',
        'published_at',
        'archived_at',
        'archived_comment',
        'post_archived_reason_id',
    ];

    protected $casts = [
        'is_rebate' => 'boolean',
        'is_trade' => 'boolean',
        'can_ship' => 'boolean',
        'moderation_id' => 'integer',
        'published_at' => 'datetime',
        'archived_at' => 'datetime',
        'registration_geo' => 'array',
        'address_geo' => 'array',
    ];

    protected $attributes = [
        'is_rebate' => false,
        'is_trade' => false,
        'can_ship' => false,
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function attributes(): HasMany
    {
        return $this->hasMany(PostAttribute::class);
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(RefCity::class, 'ref_city_id', 'id');
    }

    public function geoItem(): BelongsTo
    {
        return $this->belongsTo(GeoItem::class);
    }

    public function archived(): BelongsTo
    {
        return $this->belongsTo(PostArchivedReason::class, 'post_archived_reason_id', 'id');
    }

    public function promotions(): HasMany
    {
        return $this->hasMany(Promotion::class, 'post_id', 'id');
    }

    public function sourceGunsBroker(): BelongsTo
    {
        return $this->belongsTo(IndexGunsBrokerPost::class, 'source', 'source');
    }
}
