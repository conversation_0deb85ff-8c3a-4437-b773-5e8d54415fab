<?php

namespace App\Models;

use App\Traits\HasSlug;
use App\Traits\ImagesConversion350Trait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Event extends Model
{
    use HasFactory;
    use HasSlug;
    use ImagesConversion350Trait;

    protected $fillable = [
        'title',
        'description',
        'image_alt',
        'place',
        'address',
        'address_geo',
        'start_date',
        'end_date',
        'content',
        'ref_city_id',
        'status',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    public function city(): BelongsTo
    {
        return $this->belongsTo(RefCity::class, 'ref_city_id', 'id');
    }
}
