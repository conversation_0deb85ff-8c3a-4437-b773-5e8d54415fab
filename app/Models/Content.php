<?php

namespace App\Models;

use App\Traits\HasSlug;
use App\Traits\ImagesConversion350Trait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Content extends Model
{
    use HasFactory;
    use HasSlug;
    use ImagesConversion350Trait;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'content',
        'image',
        'image_alt',
        'published_at',
        'status',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    protected $casts = [
        'published_at' => 'datetime',
    ];
}
