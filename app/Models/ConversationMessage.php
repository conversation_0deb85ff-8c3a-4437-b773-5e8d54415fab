<?php

namespace App\Models;

use App\Events\ConversationMessageCreated;
use App\Traits\ImagesConversion350Trait;
use Cmgmyr\Messenger\Models\Message;

class ConversationMessage extends Message
{
    use ImagesConversion350Trait;

    protected $fillable = [
        'thread_id',
        'user_id',
        'body',
    ];

    protected $dispatchesEvents = [
        'created' => ConversationMessageCreated::class,
    ];
}
