<?php

namespace App\Traits;

use Elegantly\Media\Concerns\HasMedia;
use Elegantly\Media\Definitions\MediaConversionDefinition;
use Elegantly\Media\Definitions\MediaConversionImage;
use Elegantly\Media\MediaCollection;
use Spatie\Image\Enums\AlignPosition;
use Spatie\Image\Enums\Unit;
use Spatie\Image\Image;

trait ImagesConversion350Trait
{
    use HasMedia;

    public function registerMediaCollections(): array
    {
        return [
            new MediaCollection(
                name: 'images',
                acceptedMimeTypes: [ // (optional) Specify accepted file types
                    'image/jpeg',
                    'image/png',
                    'image/webp',
                ], // If true, only the latest file will be kept
                conversions: [
                    new MediaConversionImage(
                        name: '350',
                        queued: false, // Conversion will not be generated at upload time
                        width: 350
                    ),
                    new MediaConversionDefinition(
                        name: 'full',
                        handle: function ($media, $parent, $file, $filesystem, $temporaryDirectory) {
                            $sourcePath = $filesystem->path($file);
                            $targetPath = $temporaryDirectory->path("{$media->name}-full.jpg");

                            Image::load($sourcePath)
                                ->watermark(public_path('watermark.png'), AlignPosition::Center,
                                    width: 80,
                                    widthUnit: Unit::Percent,
                                    alpha: 20
                                )
                                ->save($targetPath);

                            return $media->addConversion(
                                file: $targetPath,
                                conversionName: 'full',
                                parent: $parent
                            );
                        },
                        queued: false
                    ),
                ]
            ),
        ];
    }
}
