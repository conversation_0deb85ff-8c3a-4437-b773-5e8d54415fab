<?php

namespace App\Traits;

use Illuminate\Support\Str;

trait HasSlug
{
    /**
     * Boot the HasSlug trait for a model.
     */
    protected static function bootHasSlug(): void
    {
        static::creating(function ($model) {
            $model->generateSlug();
        });

        static::updating(function ($model) {
            if ($model->isDirty($model->getSlugSourceField())) {
                $model->generateSlug();
            }
        });
    }

    /**
     * Generate slug from the source field.
     */
    public function generateSlug(): void
    {
        $sourceField = $this->getSlugSourceField();
        $this->slug = Str::slug($this->{$sourceField});
    }

    /**
     * Get the field name that should be used to generate the slug.
     * Override this method in your model if you want to use a different field.
     */
    protected function getSlugSourceField(): string
    {
        return 'title';
    }
}
