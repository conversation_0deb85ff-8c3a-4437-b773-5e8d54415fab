<?php

namespace App\Traits;

use Illuminate\Support\Str;

/**
 * Trait HasSlug
 *
 * Автоматически генерирует slug для модели на основе указанного поля.
 * По умолчанию использует поле 'title', но можно переопределить метод getSlugSourceField().
 *
 * Пример использования:
 *
 * class Article extends Model
 * {
 *     use HasSlug;
 *
 *     // Slug будет генерироваться из поля 'title'
 * }
 *
 * Или с кастомным полем:
 *
 * class Product extends Model
 * {
 *     use HasSlug;
 *
 *     protected function getSlugSourceField(): string
 *     {
 *         return 'name';
 *     }
 * }
 */
trait HasSlug
{
    /**
     * Boot the HasSlug trait for a model.
     */
    protected static function bootHasSlug(): void
    {
        static::creating(function ($model) {
            $model->generateSlug();
        });

        static::updating(function ($model) {
            if ($model->isDirty($model->getSlugSourceField())) {
                $model->generateSlug();
            }
        });
    }

    /**
     * Generate slug from the source field.
     */
    public function generateSlug(): void
    {
        $sourceField = $this->getSlugSourceField();
        $this->slug = Str::slug($this->{$sourceField});
    }

    /**
     * Get the field name that should be used to generate the slug.
     * Override this method in your model if you want to use a different field.
     */
    protected function getSlugSourceField(): string
    {
        return 'title';
    }
}
