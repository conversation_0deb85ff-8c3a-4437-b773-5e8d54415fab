<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class ContentBulkUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'content_ids'   => ['required', 'array', 'min:1'],
            'content_ids.*' => ['integer', 'exists:contents,id'],
            'status'        => ['required', 'in:draft,published'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'content_ids'   => 'список контента',
            'content_ids.*' => 'ID контента',
            'status'        => 'статус',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'content_ids.required' => 'Необходимо выбрать хотя бы один элемент.',
            'content_ids.min'      => 'Необходимо выбрать хотя бы один элемент.',
            'content_ids.*.exists' => 'Один из выбранных элементов не существует.',
            'status.in'            => 'Недопустимый статус.',
        ];
    }
}
