<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class ContentBlocksReorderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'blocks'              => ['required', 'array', 'min:1'],
            'blocks.*.id'         => ['required', 'integer', 'exists:content_blocks,id'],
            'blocks.*.position'   => ['required', 'integer', 'min:1'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'blocks'            => 'блоки',
            'blocks.*.id'       => 'ID блока',
            'blocks.*.position' => 'позиция блока',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'blocks.required'         => 'Необходимо указать блоки для изменения порядка.',
            'blocks.*.id.required'    => 'Необходимо указать ID блока.',
            'blocks.*.id.exists'      => 'Блок не найден.',
            'blocks.*.position.min'   => 'Позиция блока должна быть больше 0.',
        ];
    }
}
