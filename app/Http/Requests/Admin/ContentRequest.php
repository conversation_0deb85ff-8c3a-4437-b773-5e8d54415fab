<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ContentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Предполагаем, что авторизация проверяется в middleware
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $contentId = $this->route('content')?->id;

        return [
            'content_category_id' => ['required', Rule::exists('content_categories', 'id')],
            'title'               => ['required', 'string', 'max:255'],
            'subtitle'            => ['nullable', 'string', 'max:255'],
            'slug'                => [
                'required', 
                'string', 
                'max:255', 
                Rule::unique('contents', 'slug')->ignore($contentId)
            ],
            'description'         => ['nullable', 'string'],
            'content'             => ['nullable', 'string'],
            'image_alt'           => ['nullable', 'string', 'max:255'],
            'published_at'        => ['nullable', 'date'],
            'status'              => ['required', Rule::in(['draft', 'published'])],
            'meta_title'          => ['required', 'string', 'max:255'],
            'meta_description'    => ['required', 'string', 'max:255'],
            'meta_keywords'       => ['nullable', 'string', 'max:255'],
            'settings'            => ['nullable', 'array'],
            'type'                => ['required', Rule::in(['text', 'image', 'gallery', 'video', 'quote', 'embed'])],

            // Блоки контента
            'blocks'              => ['sometimes', 'array'],
            'blocks.*.id'         => ['sometimes', 'integer', 'exists:content_blocks,id'],
            'blocks.*.type'       => ['required_with:blocks', Rule::in(['text', 'image', 'gallery', 'video', 'quote', 'embed'])],
            'blocks.*.data'       => ['required_with:blocks', 'array'],
            'blocks.*.position'   => ['required_with:blocks', 'integer', 'min:1'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'content_category_id' => 'категория',
            'title'               => 'заголовок',
            'subtitle'            => 'подзаголовок',
            'slug'                => 'URL адрес',
            'description'         => 'описание',
            'content'             => 'содержимое',
            'image_alt'           => 'альтернативный текст изображения',
            'published_at'        => 'дата публикации',
            'status'              => 'статус',
            'meta_title'          => 'мета заголовок',
            'meta_description'    => 'мета описание',
            'meta_keywords'       => 'ключевые слова',
            'settings'            => 'настройки',
            'type'                => 'тип контента',
            'blocks'              => 'блоки',
            'blocks.*.type'       => 'тип блока',
            'blocks.*.data'       => 'данные блока',
            'blocks.*.position'   => 'позиция блока',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'content_category_id.required' => 'Необходимо выбрать категорию.',
            'content_category_id.exists'   => 'Выбранная категория не существует.',
            'slug.unique'                  => 'Такой URL адрес уже используется.',
            'blocks.*.type.in'             => 'Недопустимый тип блока.',
            'blocks.*.position.min'        => 'Позиция блока должна быть больше 0.',
        ];
    }
}
