<?php

namespace App\Http\Controllers;

use App\Http\Requests\EventsRequest;
use App\Http\Resources\EventResource;
use App\Models\Event;
use App\Models\RefCity;
use Carbon\Carbon;
use Elegantly\Media\Models\Media;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;

class EventsController extends Controller
{
    public function index(Request $request)
    {
        if ($request->has('week_date')) {
            $start = Carbon::parse($request->input('week_date'))->startOfWeek();
        } else {
            $start = Carbon::now()->startOfWeek();
        }

        $end = $start->copy()->endOfWeek();

        $limit = request()->get('limit', 16);
        $events = Event::where('start_date', '>=', $start)
            ->where('start_date', '<=', $end)
            ->orderByDesc('start_date')
            ->where('status', 'published')
            ->with('media', 'city')
            ->paginate($limit);

        return EventResource::collection($events);
    }

    public function show(Event $event)
    {
        $event->load('media', 'city');

        return new EventResource($event);
    }

    public function moderationShow(Event $event): EventResource
    {
        $event->load('media', 'city');

        return new EventResource($event);
    }

    public function moderationIndex(): AnonymousResourceCollection
    {
        $limit = request()->get('limit', 16);
        $event = Event::orderByDesc('start_date')
            ->with(['media', 'city'])
            ->paginate($limit);

        return EventResource::collection($event);
    }

    public function store(EventsRequest $request): JsonResponse
    {
        $data = $request->validated();
        if (empty($data['start_date'])) {
            $data['start_date'] = now();
        }

        $event = Event::create($data);

        if (isset($data['city_id'])) {
            $city = RefCity::where('slug', $data['city_id'])->first();
            $event->ref_city_id = $city?->id;
            $event->save();
        }

        if ($request->hasFile('image_new')) {
            try {
                $event->addMedia(
                    file: $request->file('image_new'),
                    collectionName: 'image',
                    name: "{$event->id}-image"
                );
            } catch (\Throwable $exception) {
                Log::error($exception);
            }
        }

        return response()->json([
            'ok' => true,
            'slug' => $event->slug,
        ], 201);
    }

    public function update(EventsRequest $request, Event $event): JsonResponse
    {
        $data = $request->validated();
        if (empty($data['start_date'])) {
            $data['start_date'] = now();
        }
        $event->update($data);

        if (isset($data['city_id'])) {
            $city = RefCity::where('slug', $data['city_id'])->first();
            $event->ref_city_id = $city?->id;
            $event->save();
        }

        if ($request->hasFile('image_new')) {
            try {
                $event->addMedia(
                    file: $request->file('image_new'),
                    collectionName: 'image',
                    name: "{$event->id}-image"
                );
            } catch (\Throwable $exception) {
                Log::error($exception);
            }
        }

        return response()->json([
            'ok' => true,
            'slug' => $event->slug,
        ], 201);
    }

    public function destroy(Event $event): JsonResponse
    {
        $event->delete();

        return response()->json([
            'ok' => true,
        ], 201);
    }

    public function uploadImage(Event $event, Request $request)
    {
        foreach ($request->photos ?? [] as $image) {
            try {
                $event->addMedia(
                    file: $image,
                    collectionName: 'images',
                );
            } catch (\Throwable $exception) {
                // Will throw an error if the mime type is not included in the collection's `acceptedMimeTypes` parameter.
            }
        }

        return response()->json([
            'ok' => true,
        ], 201);
    }

    public function removeImage(Event $event, Request $request)
    {
        $media = Media::where('uuid', $request->id)
            ->where('model_type', 'App\Models\News')
            ->where('model_id', $event->id)
            ->first();

        $media->delete();

        return response()->json([
            'ok' => true,
        ], 201);
    }
}
