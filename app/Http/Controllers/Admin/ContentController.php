<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ContentBlocksReorderRequest;
use App\Http\Requests\Admin\ContentBulkUpdateRequest;
use App\Http\Requests\Admin\ContentRequest;
use App\Http\Resources\Content\ContentResource;
use App\Models\Content;
use App\Services\Admin\ContentService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class ContentController extends Controller
{
    public function __construct(
        protected ContentService $contentService
    ) {}

    /**
     * Список всех материалов с фильтрами
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $filters = $request->only(['status', 'search', 'category', 'type']);
        $perPage = $request->integer('per_page', 15);

        $contents = $this->contentService->getContentList($filters, $perPage);

        return ContentResource::collection($contents);
    }

    /**
     * Создание нового контента
     */
    public function store(ContentRequest $request): JsonResponse
    {
        $content = $this->contentService->createContent($request->validated());

        return response()->json([
            'message' => 'Контент успешно создан',
            'data'    => new ContentResource($content),
        ], 201);
    }

    /**
     * Получение детальной информации о контенте
     */
    public function show(Content $content): JsonResponse
    {
        $content->load(['category', 'blocks']);

        return response()->json([
            'data' => new ContentResource($content),
        ]);
    }

    /**
     * Обновление контента
     */
    public function update(ContentRequest $request, Content $content): JsonResponse
    {
        $updatedContent = $this->contentService->updateContent($content, $request->validated());

        return response()->json([
            'message' => 'Контент успешно обновлен',
            'data'    => new ContentResource($updatedContent),
        ]);
    }

    /**
     * Удаление контента
     */
    public function destroy(Content $content): JsonResponse
    {
        $this->contentService->deleteContent($content);

        return response()->json([
            'message' => 'Контент успешно удален',
        ], 204);
    }

    /**
     * Получение статистики по контенту
     */
    public function stats(): JsonResponse
    {
        $stats = $this->contentService->getContentStats();

        return response()->json([
            'data' => $stats,
        ]);
    }

    /**
     * Массовое изменение статуса
     */
    public function bulkUpdateStatus(ContentBulkUpdateRequest $request): JsonResponse
    {
        $updatedCount = $this->contentService->bulkUpdateStatus(
            $request->input('content_ids'),
            $request->input('status')
        );

        return response()->json([
            'message' => "Статус изменен для {$updatedCount} элементов",
            'updated_count' => $updatedCount,
        ]);
    }

    /**
     * Дублирование контента
     */
    public function duplicate(Content $content): JsonResponse
    {
        $duplicatedContent = $this->contentService->duplicateContent($content);

        return response()->json([
            'message' => 'Контент успешно дублирован',
            'data'    => new ContentResource($duplicatedContent),
        ], 201);
    }

    /**
     * Изменение порядка блоков
     */
    public function reorderBlocks(ContentBlocksReorderRequest $request, Content $content): JsonResponse
    {
        foreach ($request->input('blocks') as $blockData) {
            $content->blocks()
                ->where('id', $blockData['id'])
                ->update(['position' => $blockData['position']]);
        }

        return response()->json([
            'message' => 'Порядок блоков успешно изменен',
        ]);
    }
}
