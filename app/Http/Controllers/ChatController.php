<?php

namespace App\Http\Controllers;

use App\Events\ConversationMessageCreated;
use App\Http\Resources\ConversationThreadResource;
use App\Models\ConversationMessage;
use App\Models\ConversationParticipant;
use App\Models\ConversationThread;
use App\Models\Post;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Vinkla\Hashids\Facades\Hashids;

/**
 * Контроллер для управления чатами и сообщениями
 */
class ChatController extends Controller
{
    /**
     * Создает или возвращает чат поддержки для пользователя
     */
    public function supportChat(): JsonResponse
    {
        $user = Auth::user();
        $thread_id = $user->support_chat_id;
        $thread = null;

        if ($thread_id) {
            $thread = ConversationThread::find($thread_id);
        }

        if (! $thread) {
            $support_user = User::where('email', User::SUPPORT_USER_EMAIL)->firstOrFail();

            $thread = ConversationThread::firstOrCreate([
                'subject' => 'Поддержка',
            ]);
            $thread->addParticipant($user->id);
            $thread->addParticipant($support_user->id);

            ConversationMessage::create([
                'thread_id' => $thread->id,
                'user_id' => $support_user->id,
                'body' => 'Добро пожаловать на GunPost.ru!',
            ]);

            $user->support_chat_id = $thread->id;
            $user->save();
        }

        return response()->json([
            'ok' => true,
            'id' => Hashids::encode($thread->id),
        ]);
    }

    /**
     * Получает список всех чатов пользователя
     */
    public function index(): AnonymousResourceCollection
    {
        $user = Auth::user();

        // Получаем все чаты, кроме чата поддержки
        $threads = ConversationThread::forUser($user->id)
            ->whereHas('messages')
            ->where('conversation_threads.id', '!=', $user->support_chat_id)
            ->distinct()
            ->latest('updated_at')
            ->paginate();

        // Если есть чат поддержки, добавляем его в начало коллекции
        if ($user->support_chat_id) {
            $supportThread = ConversationThread::find($user->support_chat_id);
            if ($supportThread) {
                $threads->prepend($supportThread);
            }
        }

        $threads->load('source', 'latest', 'latest.user');

        return ConversationThreadResource::collection($threads);
    }

    /**
     * Показывает конкретный чат по его хешированному ID
     *
     * @param  string  $hashId  Хешированный ID чата
     */
    public function show(string $hashId)
    {
        $hashId = Hashids::decode($hashId)[0] ?? null;

        if (! $hashId) {
            return response()->json([
                'ok' => false,
            ], 404);
        }

        $thread = ConversationThread::findOrFail($hashId);

        $userId = Auth::id();

        $thread->markAsRead($userId);
        $thread->load(['source', 'messages' => function ($query) {
            $query->orderBy('created_at', 'asc');
        }, 'messages.user', 'messages.media']);

        return ConversationThreadResource::make($thread);
    }

    /**
     * Создает новый чат для обсуждения поста
     *
     * @param  Post  $post  Модель поста
     */
    public function createPostThread(Post $post, Request $request): JsonResponse
    {
        $userId = Auth::id();

        // Проверяем, есть ли уже чат между этими пользователями для данного объявления
        $thread = ConversationThread::where('source_id', $post->id)
            ->where('source_type', Post::class)
            ->whereHas('participants', function ($query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->whereHas('participants', function ($query) use ($post) {
                $query->where('user_id', $post->user_id);
            })
            ->first();

        // Если чат не найден, создаем новый
        if (! $thread) {
            $thread = new ConversationThread([
                'source_id' => $post->id,
                'source_type' => Post::class,
                'subject' => $post->title,
            ]);
            $thread->save();

            // Добавляем участников чата
            $thread->addParticipant($userId);
            $thread->addParticipant($post->user_id);
        }

        // Добавляем сообщение, если оно есть
        if ($request->input('message')) {
            ConversationMessage::create([
                'thread_id' => $thread->id,
                'user_id' => $userId,
                'body' => $request->input('message'),
            ]);

            // Обновляем время обновления треда, чтобы он поднялся в списке
            $thread->touch();
        }

        return response()->json([
            'ok' => true,
            'id' => Hashids::encode($thread->id),
        ]);
    }

    /**
     * Отправляет сообщение в чат
     *
     * @param  string  $hashId  Хешированный ID чата
     */
    public function sendMessage(string $hashId, Request $request): JsonResponse
    {
        $hashId = Hashids::decode($hashId)[0];
        $thread = ConversationThread::findOrFail($hashId);
        $userId = Auth::id();

        // Используем транзакцию для создания сообщения и добавления фотографий
        DB::transaction(function () use ($thread, $userId, $request) {
            // Отключаем автоматическую отправку события
            $message = new ConversationMessage([
                'thread_id' => $thread->id,
                'user_id' => $userId,
                'body' => $request->input('message'),
            ]);

            // Сохраняем сообщение без отправки события
            $message->saveQuietly();

            // Добавляем фотографии к сообщению
            foreach ($request->photos ?? [] as $image) {
                try {
                    $message->addMedia(
                        file: $image,
                        collectionName: 'images',
                    );
                } catch (\Throwable $exception) {
                    Log::error($exception->getMessage());
                }
            }

            // Перезагружаем сообщение с медиафайлами и пользователем
            $message->load(['user', 'media']);

            // Вручную отправляем событие с загруженными фотографиями
            event(new ConversationMessageCreated($message));

            return $message;
        });

        $participant = ConversationParticipant::firstOrCreate([
            'thread_id' => $thread->id,
            'user_id' => $userId,
        ]);
        $participant->last_read = new Carbon;
        $participant->save();

        return response()->json([
            'ok' => true,
        ]);
    }

    /**
     * Отмечает сообщения в чате как прочитанные
     *
     * @param  string  $hashId  Хешированный ID чата
     */
    public function markMessageAsRead(string $hashId): JsonResponse
    {
        $hashId = Hashids::decode($hashId)[0];
        $thread = ConversationThread::findOrFail($hashId);

        $participant = ConversationParticipant::firstOrCreate([
            'thread_id' => $thread->id,
            'user_id' => Auth::id(),
        ]);

        $participant->last_read = new Carbon;
        $participant->save();

        return response()->json([
            'ok' => true,
        ]);
    }
}
