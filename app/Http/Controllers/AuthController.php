<?php

namespace App\Http\Controllers;

use App\Http\Requests\LoginViaPasswordRequest;
use App\Http\Resources\UserResource;
use App\Models\Post;
use App\Models\RefCity;
use App\Models\User;
use App\Models\UserConfirmation;
use App\Services\Verification\VerificationTelegramGatewayService;
use App\Services\Verification\VerificationTelegramMessageService;
use App\Services\Verification\VerificationZvonokComService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Propaganistas\LaravelPhone\PhoneNumber;

class AuthController extends Controller
{
    public function __construct(
        private VerificationZvonokComService $zvonokService,
        //        private VerificationTelegramGatewayService $telegramGatewayService,
        private VerificationTelegramMessageService $telegramMessageService
    ) {}

    public function user(): JsonResponse
    {
        $user = Auth::user()
            ->load(['city', 'confirmations', 'telegram', 'media', 'wallet'])
            ->loadCount(['unreadNotifications', 'favorites']);

        return response()->json(new UserResource($user));
    }

    public function login(LoginViaPasswordRequest $request)
    {
        $string_phone = new PhoneNumber($request->phone, 'RU');
        $string_phone = $string_phone->formatE164();

        if (Auth::attempt(['phone' => $string_phone, 'password' => $request->password], true)) {
            $user = User::where('phone', $string_phone)->first();

            if ($request->has('uuid')) {
                Post::withoutGlobalScopes()
                    ->where('fingerprint', $request->input('uuid'))
                    ->whereNull('user_id')
                    ->update(['user_id' => $user->id, 'fingerprint' => null]);
            }

            $request->session()->regenerate();

            return response()->noContent();
        }

        return response()->json([
            'errors' => [
                'password' => ['Неверный номер телефона или пароль.'],
            ],
        ], 422);
    }

    public function logout(Request $request)
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return response()->noContent();
    }

    /**
     * @throws Exception
     */
    public function sendCode(Request $request): JsonResponse
    {
        $phone = new PhoneNumber($request->phone, 'RU');
        $formattedPhone = $phone->formatE164();

        // Проверяем, есть ли у пользователя связанный Telegram-аккаунт
        $user = User::where('phone', $formattedPhone)->whereHas('telegram')->first();

        if ($user) {
            // Если есть Telegram, отправляем код через Telegram
            $type = 'telegram';
            $success = $this->telegramMessageService->sendCode($formattedPhone);
        } else {
            // Если нет Telegram, отправляем код через Zvonok.com
            $type = 'phone';
            $success = $this->zvonokService->sendCode($formattedPhone);
        }

        if ($success) {
            return response()->json([
                'success' => true,
                'type' => $type,
                'phone' => $formattedPhone,
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Не удалось отправить код',
        ], 422);
    }

    public function verifyCode(Request $request)
    {
        $phone = new PhoneNumber($request->phone, 'RU');
        $phone = $phone->formatE164();

        // Используем любой сервис для проверки кода, т.к. все они используют один и тот же механизм
        if (! $this->telegramMessageService->verifyCode($phone, $request->code)) {
            return response()->json([
                'ok' => false,
                'message' => 'Неверный код авторизации',
                'errors' => [
                    'pin' => ['Неверный код авторизации'],
                ],
            ], 422);
        }

        $user = User::where('phone', $phone)->first();
        $city = RefCity::where('slug', $request->city_id)->first();

        if ($user) {
            $user->update([
                'phone_verified_at' => now(),
            ]);
        } else {
            $user = User::create([
                'phone' => $phone,
                'name' => 'Пользователь',
                'ref_city_id' => $city?->id,
                'phone_verified_at' => now(),
            ]);
        }

        if (! $user) {
            return response()->json([
                'ok' => false,
                'message' => 'Не удалось авторизовать пользователя',
            ], 422);
        }

        if ($request->has('uuid') && $user->id) {
            Post::withoutGlobalScopes()
                ->where('fingerprint', $request->input('uuid'))
                ->whereNull('user_id')
                ->update(['user_id' => $user->id, 'fingerprint' => null]);
            Log::info('User registered', ['phone' => $phone, 'user_id' => $user->id, 'uuid' => $request->input('uuid')]);
        }

        foreach ($request->consents as $name => $consent) {
            $hasType = in_array($name, UserConfirmation::BANNERS);

            if ($hasType) {
                $user->confirmations()->firstOrCreate([
                    'type' => $name,
                ]);
            }
        }

        Auth::login($user, true);
        $request->session()->regenerate();

        return response()->noContent();
    }
}
