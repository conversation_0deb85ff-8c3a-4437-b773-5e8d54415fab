<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return response()->json([
                'message' => 'Необходима авторизация'
            ], 401);
        }

        if (auth()->user()->role !== 'admin') {
            return response()->json([
                'message' => 'Недостаточно прав доступа'
            ], 403);
        }

        return $next($request);
    }
}
