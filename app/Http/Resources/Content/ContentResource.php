<?php

namespace App\Http\Resources\Content;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ContentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id'                  => $this->id,
            'content_category_id' => $this->content_category_id,
            'title'               => $this->title,
            'subtitle'            => $this->subtitle,
            'slug'                => $this->slug,
            'description'         => $this->description,
            'content'             => $this->content,
            'image_alt'           => $this->image_alt,
            'published_at'        => $this->published_at?->format('Y-m-d H:i:s'),
            'status'              => $this->status,
            'meta_title'          => $this->meta_title,
            'meta_description'    => $this->meta_description,
            'meta_keywords'       => $this->meta_keywords,
            'settings'            => $this->settings,
            'type'                => $this->type,
            'created_at'          => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at'          => $this->updated_at?->format('Y-m-d H:i:s'),

            // Связи
            'category'            => new ContentCategoryResource($this->whenLoaded('category')),
            'blocks'              => ContentBlockResource::collection($this->whenLoaded('blocks')),
        ];
    }
}
