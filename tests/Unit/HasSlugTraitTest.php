<?php

namespace Tests\Unit;

use Illuminate\Support\Str;
use PHPUnit\Framework\TestCase;

class HasSlugTraitTest extends TestCase
{
    public function test_str_slug_function_works_correctly()
    {
        // Простой тест функции Str::slug, которую использует наш trait
        $title = 'Тестовая статья о охоте';
        $slug = Str::slug($title);

        $this->assertEquals('testovaia-statia-o-oxote', $slug);
    }

    public function test_str_slug_with_different_text()
    {
        $title = 'Пользовательское название';
        $slug = Str::slug($title);

        $this->assertEquals('polzovatelskoe-nazvanie', $slug);
    }
}
