<?php

namespace Tests\Unit;

use App\Models\Content;
use App\Models\News;
use App\Models\Event;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class HasSlugTraitTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_generates_slug_from_title_when_creating_content()
    {
        $content = Content::create([
            'title' => 'Тестовая статья о охоте',
            'description' => 'Описание статьи',
            'content' => 'Содержимое статьи',
            'status' => 'published',
        ]);

        $this->assertEquals('testovaia-statia-o-okhote', $content->slug);
    }

    /** @test */
    public function it_updates_slug_when_title_changes_for_news()
    {
        $news = News::create([
            'title' => 'Первоначальный заголовок',
            'description' => 'Описание новости',
            'content' => 'Содержимое новости',
            'status' => 'published',
        ]);

        $this->assertEquals('pervonachalnyi-zagolovok', $news->slug);

        $news->update(['title' => 'Обновленный заголовок']);

        $this->assertEquals('obnovlennyi-zagolovok', $news->fresh()->slug);
    }

    /** @test */
    public function it_does_not_update_slug_when_other_fields_change_for_event()
    {
        $event = Event::create([
            'title' => 'Охотничье мероприятие',
            'description' => 'Описание мероприятия',
            'place' => 'Место проведения',
            'start_date' => now(),
            'end_date' => now()->addDays(1),
            'status' => 'published',
        ]);

        $originalSlug = $event->slug;
        $this->assertEquals('okhotniche-meropriiatie', $originalSlug);

        $event->update(['description' => 'Новое описание мероприятия']);

        $this->assertEquals($originalSlug, $event->fresh()->slug);
    }
}
