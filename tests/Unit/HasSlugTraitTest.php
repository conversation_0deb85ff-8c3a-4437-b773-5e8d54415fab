<?php

namespace Tests\Unit;

use App\Traits\HasSlug;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use PHPUnit\Framework\TestCase;

class HasSlugTraitTest extends TestCase
{
    public function test_generates_slug_from_title()
    {
        $model = new TestModelWithSlug();
        $model->title = 'Тестовая статья о охоте';
        $model->generateSlug();

        $this->assertEquals('testovaia-statia-o-okhote', $model->slug);
    }

    public function test_gets_correct_slug_source_field()
    {
        $model = new TestModelWithSlug();
        $this->assertEquals('title', $model->getSlugSourceField());
    }

    public function test_can_override_slug_source_field()
    {
        $model = new TestModelWithCustomSlugSource();
        $this->assertEquals('name', $model->getSlugSourceField());
    }

    public function test_generates_slug_from_custom_source_field()
    {
        $model = new TestModelWithCustomSlugSource();
        $model->name = 'Пользовательское название';
        $model->generateSlug();

        $this->assertEquals('polzovatelskoe-nazvanie', $model->slug);
    }
}

// Тестовые модели для проверки trait
class TestModelWithSlug
{
    use HasSlug;

    public $title;
    public $slug;

    public function getSlugSourceField(): string
    {
        return parent::getSlugSourceField();
    }

    public function isDirty($field): bool
    {
        return true; // Для простоты всегда возвращаем true
    }
}

class TestModelWithCustomSlugSource
{
    use HasSlug;

    public $name;
    public $slug;

    protected function getSlugSourceField(): string
    {
        return 'name';
    }

    public function isDirty($field): bool
    {
        return true;
    }
}
