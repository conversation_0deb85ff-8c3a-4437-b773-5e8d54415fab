<?php

namespace Tests\Feature\Admin;

use App\Models\Content;
use App\Models\ContentCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ContentControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected ContentCategory $category;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->category = ContentCategory::factory()->create();
    }

    public function test_admin_can_get_content_list()
    {
        Content::factory()->count(3)->create(['content_category_id' => $this->category->id]);

        $response = $this->actingAs($this->admin)
            ->getJson('/api/admin/content');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'title',
                        'slug',
                        'status',
                        'category',
                    ]
                ]
            ]);
    }

    public function test_admin_can_create_content()
    {
        $contentData = [
            'content_category_id' => $this->category->id,
            'title' => 'Тестовая статья',
            'slug' => 'test-article',
            'description' => 'Описание статьи',
            'content' => 'Содержимое статьи',
            'status' => 'draft',
            'meta_title' => 'Мета заголовок',
            'meta_description' => 'Мета описание',
            'type' => 'text',
        ];

        $response = $this->actingAs($this->admin)
            ->postJson('/api/admin/content', $contentData);

        $response->assertStatus(201)
            ->assertJsonFragment(['title' => 'Тестовая статья']);

        $this->assertDatabaseHas('contents', [
            'title' => 'Тестовая статья',
            'slug' => 'test-article',
        ]);
    }

    public function test_admin_can_update_content()
    {
        $content = Content::factory()->create(['content_category_id' => $this->category->id]);

        $updateData = [
            'content_category_id' => $this->category->id,
            'title' => 'Обновленный заголовок',
            'slug' => $content->slug,
            'status' => 'published',
            'meta_title' => 'Мета заголовок',
            'meta_description' => 'Мета описание',
            'type' => 'text',
        ];

        $response = $this->actingAs($this->admin)
            ->putJson("/api/admin/content/{$content->id}", $updateData);

        $response->assertStatus(200)
            ->assertJsonFragment(['title' => 'Обновленный заголовок']);

        $this->assertDatabaseHas('contents', [
            'id' => $content->id,
            'title' => 'Обновленный заголовок',
        ]);
    }

    public function test_admin_can_delete_content()
    {
        $content = Content::factory()->create(['content_category_id' => $this->category->id]);

        $response = $this->actingAs($this->admin)
            ->deleteJson("/api/admin/content/{$content->id}");

        $response->assertStatus(204);

        $this->assertDatabaseMissing('contents', [
            'id' => $content->id,
        ]);
    }

    public function test_non_admin_cannot_access_admin_routes()
    {
        $user = User::factory()->create(['role' => 'user']);

        $response = $this->actingAs($user)
            ->getJson('/api/admin/content');

        $response->assertStatus(403);
    }

    public function test_guest_cannot_access_admin_routes()
    {
        $response = $this->getJson('/api/admin/content');

        $response->assertStatus(401);
    }

    public function test_admin_can_get_content_stats()
    {
        Content::factory()->count(2)->create([
            'content_category_id' => $this->category->id,
            'status' => 'published'
        ]);
        Content::factory()->create([
            'content_category_id' => $this->category->id,
            'status' => 'draft'
        ]);

        $response = $this->actingAs($this->admin)
            ->getJson('/api/admin/content/stats');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'total',
                    'published',
                    'draft',
                    'by_type',
                ]
            ]);
    }

    public function test_admin_can_bulk_update_status()
    {
        $contents = Content::factory()->count(3)->create([
            'content_category_id' => $this->category->id,
            'status' => 'draft'
        ]);

        $response = $this->actingAs($this->admin)
            ->postJson('/api/admin/content/bulk-update-status', [
                'content_ids' => $contents->pluck('id')->toArray(),
                'status' => 'published',
            ]);

        $response->assertStatus(200)
            ->assertJsonFragment(['updated_count' => 3]);

        foreach ($contents as $content) {
            $this->assertDatabaseHas('contents', [
                'id' => $content->id,
                'status' => 'published',
            ]);
        }
    }
}
