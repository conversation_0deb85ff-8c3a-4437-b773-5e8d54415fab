# Admin Content Management System

Система управления контентом для административной панели Laravel приложения.

## Структура проекта

### Модели
- `Content` - Основная модель контента
- `ContentCategory` - Категории контента  
- `ContentBlock` - Блоки контента (для составного контента)

### Контроллеры
- `App\Http\Controllers\Admin\ContentController` - Основной контроллер для управления контентом

### Request классы
- `App\Http\Requests\Admin\ContentRequest` - Валидация основных операций с контентом
- `App\Http\Requests\Admin\ContentBulkUpdateRequest` - Валидация массовых операций
- `App\Http\Requests\Admin\ContentBlocksReorderRequest` - Валидация изменения порядка блоков

### Resource классы
- `App\Http\Resources\Admin\ContentResource` - Форматирование ответов API для контента
- `App\Http\Resources\Admin\ContentCategoryResource` - Форматирование ответов для категорий
- `App\Http\Resources\Admin\ContentBlockResource` - Форматирование ответов для блоков

### Сервисы
- `App\Services\Admin\ContentService` - Бизнес-логика управления контентом

### Traits
- `App\Traits\HasSlug` - Автоматическая генерация slug для моделей

## Функциональность

### Основные операции CRUD
- ✅ Создание контента
- ✅ Получение списка с фильтрами
- ✅ Получение детальной информации
- ✅ Обновление контента
- ✅ Удаление контента

### Дополнительные функции
- ✅ Статистика по контенту
- ✅ Массовое изменение статуса
- ✅ Дублирование контента
- ✅ Управление блоками контента
- ✅ Изменение порядка блоков

### Типы блоков
- `text` - Текстовый блок
- `image` - Одиночное изображение
- `gallery` - Галерея изображений
- `video` - Встроенное видео
- `quote` - Цитата
- `embed` - Встроенный код (iframe, etc.)

## Установка и настройка

### 1. Миграции
```bash
php artisan migrate
```

### 2. Регистрация middleware
Добавьте в `app/Http/Kernel.php`:
```php
protected $routeMiddleware = [
    // ...
    'admin' => \App\Http\Middleware\AdminMiddleware::class,
];
```

### 3. Подключение маршрутов
В `routes/api.php` добавьте:
```php
Route::prefix('admin')->group(base_path('routes/admin.php'));
```

### 4. Создание фабрик (для тестирования)
```bash
php artisan make:factory ContentFactory
php artisan make:factory ContentCategoryFactory
php artisan make:factory ContentBlockFactory
```

## Использование

### Создание контента через API

```bash
curl -X POST http://your-app.com/api/admin/content \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "content_category_id": 1,
    "title": "Новая статья",
    "slug": "novaya-statya",
    "description": "Описание статьи",
    "content": "Основное содержимое",
    "status": "draft",
    "meta_title": "Мета заголовок",
    "meta_description": "Мета описание",
    "type": "text",
    "blocks": [
      {
        "type": "text",
        "data": {
          "content": "Первый блок текста"
        },
        "position": 1
      }
    ]
  }'
```

### Использование в коде

```php
use App\Services\Admin\ContentService;

$contentService = app(ContentService::class);

// Создание контента
$content = $contentService->createContent([
    'title' => 'Заголовок',
    'content_category_id' => 1,
    // ... другие поля
]);

// Получение списка с фильтрами
$contents = $contentService->getContentList([
    'status' => 'published',
    'search' => 'поиск',
]);

// Статистика
$stats = $contentService->getContentStats();
```

## Тестирование

### Запуск тестов
```bash
php artisan test tests/Feature/Admin/ContentControllerTest.php
```

### Создание тестовых данных
```php
use App\Models\Content;
use App\Models\ContentCategory;

$category = ContentCategory::factory()->create();
$content = Content::factory()->create([
    'content_category_id' => $category->id
]);
```

## Безопасность

### Middleware
- `auth:sanctum` - Проверка аутентификации
- `admin` - Проверка роли администратора

### Валидация
Все входящие данные проходят валидацию через Request классы:
- Проверка существования связанных моделей
- Валидация уникальности slug
- Проверка допустимых значений для enum полей
- Санитизация входных данных

### Авторизация
```php
// В контроллере или middleware
if (auth()->user()->role !== 'admin') {
    abort(403, 'Недостаточно прав доступа');
}
```

## Расширение функциональности

### Добавление нового типа блока

1. Обновите enum в миграции `contents` таблицы
2. Добавьте валидацию в `ContentRequest`
3. Обновите фабрику `ContentBlockFactory`
4. Добавьте обработку в frontend

### Добавление новых фильтров

1. Обновите метод `getContentList` в `ContentService`
2. Добавьте валидацию в контроллер
3. Обновите документацию API

## Производительность

### Оптимизация запросов
- Используется eager loading для связей
- Пагинация для больших списков
- Индексы на часто используемые поля

### Кэширование
Рекомендуется добавить кэширование для:
- Статистики контента
- Списка категорий
- Популярного контента

```php
Cache::remember('content.stats', 3600, function () {
    return $this->contentService->getContentStats();
});
```
